import React from "react";

const AdminDashboard = () => {
  return (
    <div className="flex min-h-screen bg-gray-100">
      {/* Sidebar */}
      <aside className="w-64 bg-white shadow-md">
        <div className="p-6 font-bold text-xl border-b">Admin Panel</div>
        <nav className="mt-6">
          <ul>
            <li className="py-2 px-6 hover:bg-gray-200 cursor-pointer">Dashboard</li>
            <li className="py-2 px-6 hover:bg-gray-200 cursor-pointer">Users</li>
            <li className="py-2 px-6 hover:bg-gray-200 cursor-pointer">Settings</li>
            <li className="py-2 px-6 hover:bg-gray-200 cursor-pointer">Logout</li>
          </ul>
        </nav>
      </aside>
      {/* Main Content */}
      <main className="flex-1 p-8">
        <header className="mb-8">
          <h1 className="text-3xl font-bold">Welcome, Admin!</h1>
          <p className="text-gray-600 mt-2">Here is your dashboard overview.</p>
        </header>
        <section className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded shadow">
            <h2 className="text-xl font-semibold mb-2">Total Users</h2>
            <p className="text-2xl font-bold">120</p>
          </div>
          <div className="bg-white p-6 rounded shadow">
            <h2 className="text-xl font-semibold mb-2">Active Sessions</h2>
            <p className="text-2xl font-bold">15</p>
          </div>
          <div className="bg-white p-6 rounded shadow">
            <h2 className="text-xl font-semibold mb-2">System Health</h2>
            <p className="text-2xl font-bold text-green-500">Good</p>
          </div>
        </section>
      </main>
    </div>
  );
};

export default AdminDashboard;